# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=https://azureaifoundry8088282337.openai.azure.com/
AZURE_OPENAI_API_KEY=BmejuaLGA7PIqxh9vcm2XMHDfNKsmxGEdulOpac0opl3kR0wrT8mJQQJ99BCACfhMk5XJ3w3AAAAACOGTHSc
AZURE_OPENAI_DEPLOYMENT_NAME=azureaifoundry8088282337
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-3-large
AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-4.1

# Qdrant Configuration
QDRANT_URL=https://fd2b662d-fea8-438c-b900-9f810cd27386.us-west-1-0.aws.cloud.qdrant.io:6333
QDRANT_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.GQLtTF5HUGPGQnhzAxrFRZF-N17NAVN_kNT271pkJvU
COLLECTION_NAME=sql_injection

# API Server Configuration
API_HOST=0.0.0.0
API_PORT=8001

#websearch
AZURE_BING_SEARCH_ENDPOINT=https://api-kiizi.swedencentral.inference.ml.azure.com/score
AZURE_BING_SEARCH_KEY=Axx6Q7Ylctn53I9Rm1eLamsFSybCHGYEFlvLsk34sAgppp1yNX9TJQQJ99BFAAAAAAAAAAAAINFRAZMLRCRd

WEB_SEARCH_BACKEND=google
GOOGLE_CSE_API_KEY=AIzaSyCtDvB4pT3xSIEQiewNIfJw9AAqeB-obv4
GOOGLE_CSE_CX=402c753aa1ced4f8f

# Google Gemini Configuration
GEMINI_API_KEY=AIzaSyBj0TK4W3ua2Ux5hchDiAZUqRSS-ivZs3w
GEMINI_MODEL_PROVIDER=google_ai_studio  # Options: "google_ai_studio", "vertex_ai"
GEMINI_CHAT_MODEL=models/gemini-2.5-flash-preview-05-20	
GEMINI_EMBEDDING_MODEL=models/gemini-embedding-exp-03-07


MODEL_PROVIDER=gemini


# Vertex AI Configuration (if using vertex_ai provider)
# GOOGLE_CLOUD_PROJECT=your_gcp_project_id
# GOOGLE_CLOUD_LOCATION=us-central1
# GOOGLE_GENAI_USE_VERTEXAI=true

