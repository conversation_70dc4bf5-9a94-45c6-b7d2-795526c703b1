# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a script, but it is better to
#  explicitly exclude them from version control, since they are archives
#  and generated files.
*.spec

# Installer logs
_build/

# Environments
.env
.venv
venv/
ENV/
env/

# Spyder project settings
.spyderproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# pytest
.pytest_cache/

##custom

#repomix
repomix-output.xml