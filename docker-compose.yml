services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8001
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}
      - GOOGLE_CSE_API_KEY=${GOOGLE_CSE_API_KEY:-}
      - GOOGLE_CSE_CX=${GOOGLE_CSE_CX:-}
      - QDRANT_URL=${QDRANT_URL:-}
      - QDRANT_API_KEY=${QDRANT_API_KEY:-}
      - COLLECTION_NAME=${COLLECTION_NAME:-}
    volumes:
      - ./backend/src:/app/src
    restart: unless-stopped
    networks:
      - algobrain-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "8080:80"
    environment:
      - VITE_API_URL=http://localhost:8001
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - algobrain-network

networks:
  algobrain-network:
    driver: bridge