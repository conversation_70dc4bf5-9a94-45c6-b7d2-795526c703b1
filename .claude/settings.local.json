{"permissions": {"allow": ["mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm create:*)", "Bash(npm install)", "Bash(npx tailwindcss init:*)", "Bash(npm uninstall:*)", "Bash(npm install:*)", "Bash(rm:*)", "<PERSON><PERSON>(curl:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_click", "mcp__figma__get_figma_data", "mcp__playwright__browser_close", "<PERSON><PERSON>(docker-compose:*)", "mcp__docker-mcp__get-logs", "WebFetch(domain:python.langchain.com)", "mcp__docker-mcp__list-containers", "mcp__zen__planner", "Bash(npm run lint)", "Bash(npm run build:*)", "Bash(pip install:*)", "<PERSON><PERSON>(docker exec:*)", "mcp__zen__debug", "Bash(grep:*)", "mcp__playwright__browser_wait_for"], "deny": []}}