export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'code' | 'vulnerability';
}

export interface ChatState {
  messages: Message[];
  isLoading: boolean;
  error?: string;
}

export interface VulnerabilityData {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  payload?: string;
  url?: string;
  timestamp: Date;
}

export interface AgentResponse {
  response: string;
  vulnerabilities?: VulnerabilityData[];
  recommendations?: string[];
}

export interface ApiConfig {
  baseUrl: string;
  timeout: number;
}

// HTTP Request/Response Types for Payload Suggestor
export interface HttpRequest {
  method: string;
  url: string;
  headers: Record<string, string>;
  body: string;
  positions?: PositionMarker[];
}

export interface HttpResponse {
  status_code: number;
  headers: Record<string, string>;
  body: string;
}

export interface PositionMarker {
  id: string;
  location: 'url' | 'header' | 'body' | 'url_parameter' | 'form_data' | 'json_body';
  field?: string;
  startIndex: number;
  endIndex: number;
  type: 'parameter' | 'value' | 'path';
  description?: string;
}

// Payload Suggestion Types
export interface PayloadSuggestion {
  payload: string;
  type: 'boolean_blind' | 'union_based' | 'time_based' | 'error_based' | 'stacked_queries';
  description: string;
  risk_level: 'low' | 'medium' | 'high';
  applicable_points: string[];
}

export interface InjectionPoint {
  location: 'url_parameter' | 'form_data' | 'json_body' | 'header';
  parameter: string;
  value: string;
  position: {
    type: string;
    parameter_name?: string;
    parameter_index?: number;
    key?: string;
    header_name?: string;
  };
  risk_level: 'low' | 'medium' | 'high';
}

export interface VulnerabilityIndicator {
  type: 'sql_error_pattern' | 'database_error' | 'error_status_code';
  pattern?: string;
  match?: string;
  position?: number;
  severity: 'low' | 'medium' | 'high';
  database_type?: string;
  keyword?: string;
  status_code?: number;
}

export interface PayloadRecommendation {
  injection_point: InjectionPoint;
  recommended_payload: string;
  full_test_url: string;
  reasoning: string;
  next_steps: string[];
}

export interface PayloadAnalysisResult {
  injection_points: InjectionPoint[];
  payload_suggestions: PayloadSuggestion[];
  vulnerability_indicators: VulnerabilityIndicator[];
  recommended_payloads: PayloadRecommendation[];
}

// Payload Suggestor Agent Types
export interface PayloadSuggestorRequest {
  request: HttpRequest;
  response: HttpResponse;
  user_message?: string;
}

export interface PayloadSuggestorResponse {
  analysis_result: PayloadAnalysisResult;
  agent_message: string;
  suggested_actions: string[];
}

// Studio Interface Types
export interface StudioState {
  httpRequest: HttpRequest;
  httpResponse: HttpResponse;
  payloadSuggestions: PayloadSuggestion[];
  selectedPayload: PayloadSuggestion | null;
  chatMessages: Message[];
  isAnalyzing: boolean;
}

// Drag and Drop Types
export interface DraggedPayload {
  payload: PayloadSuggestion;
  sourceIndex: number;
}

export interface DropTarget {
  targetType: 'request_editor' | 'parameter' | 'header' | 'body';
  targetPosition?: number;
  targetField?: string;
}