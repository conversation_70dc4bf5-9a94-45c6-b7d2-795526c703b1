import React from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import type { VulnerabilityData } from '../../types';

interface VulnerabilityChartProps {
  vulnerabilities: VulnerabilityData[];
  className?: string;
}

const VulnerabilityChart: React.FC<VulnerabilityChartProps> = ({ vulnerabilities, className = '' }) => {
  // Process data for charts
  const severityData = vulnerabilities.reduce((acc, vuln) => {
    acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const chartData = [
    { name: 'Critical', value: severityData.critical || 0, color: '#dc2626' },
    { name: 'High', value: severityData.high || 0, color: '#ea580c' },
    { name: 'Medium', value: severityData.medium || 0, color: '#d97706' },
    { name: 'Low', value: severityData.low || 0, color: '#65a30d' },
  ];

  const typeData = vulnerabilities.reduce((acc, vuln) => {
    acc[vuln.type] = (acc[vuln.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const typeChartData = Object.entries(typeData).map(([name, value]) => ({
    name,
    value,
  }));

  const recentVulns = vulnerabilities
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 5);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-destructive">{severityData.critical || 0}</div>
            <p className="text-xs text-muted-foreground">Critical</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-orange-600">{severityData.high || 0}</div>
            <p className="text-xs text-muted-foreground">High</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-yellow-600">{severityData.medium || 0}</div>
            <p className="text-xs text-muted-foreground">Medium</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-green-600">{severityData.low || 0}</div>
            <p className="text-xs text-muted-foreground">Low</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Severity Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Vulnerability Severity Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Vulnerability Types */}
        <Card>
          <CardHeader>
            <CardTitle>Vulnerability Types</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={typeChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#0ea5e9" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Vulnerabilities */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Vulnerabilities</CardTitle>
        </CardHeader>
        <CardContent>
          {recentVulns.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">No vulnerabilities detected yet</p>
          ) : (
            <div className="space-y-3">
              {recentVulns.map((vuln) => (
                <div
                  key={vuln.id}
                  className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span
                        className={`inline-block w-2 h-2 rounded-full ${
                          vuln.severity === 'critical'
                            ? 'bg-red-500'
                            : vuln.severity === 'high'
                            ? 'bg-orange-500'
                            : vuln.severity === 'medium'
                            ? 'bg-yellow-500'
                            : 'bg-green-500'
                        }`}
                      />
                      <span className="font-medium">{vuln.type}</span>
                      <span className="text-xs bg-muted px-2 py-1 rounded">
                        {vuln.severity.toUpperCase()}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{vuln.description}</p>
                    {vuln.url && (
                      <p className="text-xs text-muted-foreground mt-1">URL: {vuln.url}</p>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {vuln.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default VulnerabilityChart;