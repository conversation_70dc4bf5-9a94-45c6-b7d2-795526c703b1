import React, { useState } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import type { 
  HttpRequest, 
  HttpResponse, 
  PayloadSuggestion, 
  PayloadAnalysisResult,
  DraggedPayload 
} from '../../types';
import PayloadSuggestorChat from './PayloadSuggestorChat';
import PayloadSuggestionPanel from './PayloadSuggestionPanel';
import './StudioInterface.css';

interface StudioInterfaceProps {
  className?: string;
}

const StudioInterface: React.FC<StudioInterfaceProps> = ({ className = '' }) => {
  // HTTP Request/Response State
  const [httpRequest, setHttpRequest] = useState<HttpRequest>({
    method: 'GET',
    url: 'https://example.com/api/users?id=1',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    body: ''
  });
  
  const [httpResponse] = useState<HttpResponse>({
    status_code: 200,
    headers: {
      'Content-Type': 'application/json'
    },
    body: '{"users": [{"id": 1, "name": "John Doe"}]}'
  });

  // Payload Analysis State
  const [payloadSuggestions, setPayloadSuggestions] = useState<PayloadSuggestion[]>([]);
  const [analysisResult, setAnalysisResult] = useState<PayloadAnalysisResult | null>(null);
  const [selectedPayload, setSelectedPayload] = useState<PayloadSuggestion | null>(null);
  const [draggedPayload, setDraggedPayload] = useState<DraggedPayload | null>(null);

  // UI State - removed setLoading as it's not used in the current implementation

  // Event Handlers
  const handlePayloadSuggestions = (suggestions: PayloadSuggestion[]) => {
    setPayloadSuggestions(suggestions);
  };

  const handleAnalysisResult = (result: PayloadAnalysisResult) => {
    setAnalysisResult(result);
  };

  const handlePayloadSelect = (payload: PayloadSuggestion) => {
    setSelectedPayload(payload);
  };

  const handlePayloadDragStart = (payload: PayloadSuggestion, index: number) => {
    setDraggedPayload({ payload, sourceIndex: index });
  };

  const handleRequestChange = (field: keyof HttpRequest, value: string | Record<string, string>) => {
    setHttpRequest(prev => ({ ...prev, [field]: value }));
  };

  const buildHttpRequestString = () => {
    const headerString = Object.entries(httpRequest.headers)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
    
    return `${httpRequest.method} ${httpRequest.url} HTTP/1.1\n${headerString}\n\n${httpRequest.body}`;
  };

  const buildHttpResponseString = () => {
    const headerString = Object.entries(httpResponse.headers)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
    
    return `HTTP/1.1 ${httpResponse.status_code} OK\n${headerString}\n\n${httpResponse.body}`;
  };

  const parseHttpRequest = (requestString: string) => {
    const lines = requestString.split('\n');
    const requestLine = lines[0];
    const [method, url] = requestLine.split(' ');
    
    const headers: Record<string, string> = {};
    let bodyStartIndex = -1;
    
    for (let i = 1; i < lines.length; i++) {
      if (lines[i].trim() === '') {
        bodyStartIndex = i + 1;
        break;
      }
      const [key, ...valueParts] = lines[i].split(':');
      if (key && valueParts.length > 0) {
        headers[key.trim()] = valueParts.join(':').trim();
      }
    }
    
    const body = bodyStartIndex >= 0 ? lines.slice(bodyStartIndex).join('\n') : '';
    
    setHttpRequest({ method: method || 'GET', url: url || '', headers, body });
  };

  const handleDrop = (e: React.DragEvent, targetType: string) => {
    e.preventDefault();
    
    if (draggedPayload && targetType === 'request_editor') {
      // Simple insertion at cursor position
      const currentRequest = buildHttpRequestString();
      const newRequest = currentRequest + '\n' + draggedPayload.payload.payload;
      parseHttpRequest(newRequest);
      setDraggedPayload(null);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  return (
    <div className={`w-full max-w-7xl mx-auto ${className}`}>
      {/* Main Studio Container */}
      <div className="bg-gray-900 rounded-2xl p-6 min-h-[700px]">
        <div className="h-full">
          <PanelGroup direction="horizontal" className="w-full h-full">
            {/* HTTP Request/Response Panel */}
            <Panel defaultSize={35} minSize={25}>
              <div className="h-full bg-gray-800 rounded-lg p-4 flex flex-col mr-2">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">HTTP Request/Response</h3>
                </div>
                
                <PanelGroup direction="vertical" className="flex-1">
                  {/* Request Editor */}
                  <Panel defaultSize={50}>
                    <div className="h-full flex flex-col">
                      <div className="flex items-center mb-2">
                        <h4 className="text-sm font-medium text-gray-300">Request</h4>
                        <select 
                          value={httpRequest.method}
                          onChange={(e) => handleRequestChange('method', e.target.value)}
                          className="ml-2 bg-gray-700 text-white text-xs px-2 py-1 rounded border border-gray-600"
                        >
                          <option value="GET">GET</option>
                          <option value="POST">POST</option>
                          <option value="PUT">PUT</option>
                          <option value="DELETE">DELETE</option>
                          <option value="PATCH">PATCH</option>
                        </select>
                      </div>
                      <div
                        className="flex-1 bg-gray-900 border border-gray-600 rounded p-3 overflow-auto"
                        onDrop={(e) => handleDrop(e, 'request_editor')}
                        onDragOver={handleDragOver}
                      >
                        <textarea
                          value={buildHttpRequestString()}
                          onChange={(e) => parseHttpRequest(e.target.value)}
                          className="w-full h-full bg-transparent text-white font-mono text-sm resize-none focus:outline-none"
                          placeholder="HTTP request will appear here..."
                        />
                      </div>
                    </div>
                  </Panel>
                  
                  <PanelResizeHandle>
                    <div className="h-3 flex items-center justify-center cursor-row-resize group">
                      <div className="w-10 h-1 bg-gray-600 rounded-full group-hover:bg-blue-500 transition-colors"></div>
                    </div>
                  </PanelResizeHandle>
                  
                  {/* Response Viewer */}
                  <Panel defaultSize={50}>
                    <div className="h-full flex flex-col">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">Response</h4>
                      <div className="flex-1 bg-gray-900 border border-gray-600 rounded p-3 overflow-auto">
                        <pre className="text-white font-mono text-sm whitespace-pre-wrap">
                          {buildHttpResponseString()}
                        </pre>
                      </div>
                    </div>
                  </Panel>
                </PanelGroup>
              </div>
            </Panel>

            <PanelResizeHandle>
              <div className="w-3 h-full flex items-center justify-center cursor-col-resize group">
                <div className="w-1 h-10 bg-gray-600 rounded-full group-hover:bg-blue-500 transition-colors"></div>
              </div>
            </PanelResizeHandle>

            {/* Chat Panel */}
            <Panel defaultSize={35} minSize={25}>
              <div className="h-full mr-2 ml-2">
                <PayloadSuggestorChat
                  httpRequest={httpRequest}
                  httpResponse={httpResponse}
                  onPayloadSuggestions={handlePayloadSuggestions}
                  onAnalysisResult={handleAnalysisResult}
                  className="h-full"
                />
              </div>
            </Panel>

            <PanelResizeHandle>
              <div className="w-3 h-full flex items-center justify-center cursor-col-resize group">
                <div className="w-1 h-10 bg-gray-600 rounded-full group-hover:bg-blue-500 transition-colors"></div>
              </div>
            </PanelResizeHandle>

            {/* Payload Suggestions Panel */}
            <Panel defaultSize={30} minSize={20}>
              <div className="h-full ml-2">
                <PayloadSuggestionPanel
                  payloadSuggestions={payloadSuggestions}
                  analysisResult={analysisResult}
                  onPayloadSelect={handlePayloadSelect}
                  onPayloadDragStart={handlePayloadDragStart}
                  className="h-full"
                />
              </div>
            </Panel>
          </PanelGroup>
        </div>

        {/* Status Bar */}
        <div className="mt-6 flex items-center justify-between text-sm text-gray-400 border-t border-gray-700 pt-4">
          <div className="flex items-center space-x-6">
            <span className="flex items-center">
              <div className="w-2 h-2 rounded-full mr-2 bg-green-500"></div>
              Ready
            </span>
            <span>
              Payloads: {payloadSuggestions.length}
            </span>
            <span>
              Injection Points: {analysisResult?.injection_points?.length || 0}
            </span>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-xs">
              {selectedPayload ? `Selected: ${selectedPayload.type}` : 'No payload selected'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudioInterface;