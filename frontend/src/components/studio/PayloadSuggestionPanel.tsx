import React from 'react';
import type { PayloadSuggestion, PayloadAnalysisResult, InjectionPoint } from '../../types';

interface PayloadSuggestionPanelProps {
  payloadSuggestions: PayloadSuggestion[];
  analysisResult: PayloadAnalysisResult | null;
  onPayloadSelect: (payload: PayloadSuggestion) => void;
  onPayloadDragStart: (payload: PayloadSuggestion, index: number) => void;
  className?: string;
}

const PayloadSuggestionPanel: React.FC<PayloadSuggestionPanelProps> = ({
  payloadSuggestions,
  analysisResult,
  onPayloadSelect,
  onPayloadDragStart,
  className = ''
}) => {
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-400 bg-red-900/30 border-red-600';
      case 'medium': return 'text-yellow-400 bg-yellow-900/30 border-yellow-600';
      case 'low': return 'text-green-400 bg-green-900/30 border-green-600';
      default: return 'text-gray-400 bg-gray-900/30 border-gray-600';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'boolean_blind': return 'bg-blue-600';
      case 'union_based': return 'bg-purple-600';
      case 'time_based': return 'bg-orange-600';
      case 'error_based': return 'bg-red-600';
      case 'stacked_queries': return 'bg-pink-600';
      default: return 'bg-gray-600';
    }
  };

  const renderInjectionPoints = () => {
    if (!analysisResult?.injection_points?.length) return null;

    return (
      <div className="mb-6">
        <h4 className="text-sm font-semibold text-gray-300 mb-3">Injection Points</h4>
        <div className="space-y-2">
          {analysisResult.injection_points.map((point: InjectionPoint, index: number) => (
            <div 
              key={index}
              className={`p-3 rounded-lg border ${getRiskColor(point.risk_level)}`}
            >
              <div className="flex items-center justify-between mb-1">
                <span className="font-medium text-sm">{point.parameter}</span>
                <span className={`text-xs px-2 py-1 rounded ${getRiskColor(point.risk_level)}`}>
                  {point.risk_level}
                </span>
              </div>
              <div className="text-xs text-gray-400">
                {point.location} • Value: {point.value.substring(0, 30)}
                {point.value.length > 30 ? '...' : ''}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderVulnerabilityIndicators = () => {
    if (!analysisResult?.vulnerability_indicators?.length) return null;

    return (
      <div className="mb-6">
        <h4 className="text-sm font-semibold text-gray-300 mb-3">Vulnerability Indicators</h4>
        <div className="space-y-2">
          {analysisResult.vulnerability_indicators.map((indicator, index) => (
            <div 
              key={index}
              className={`p-2 rounded border ${getRiskColor(indicator.severity)}`}
            >
              <div className="text-xs font-medium">{indicator.type}</div>
              {indicator.match && (
                <div className="text-xs text-gray-400 mt-1">"{indicator.match}"</div>
              )}
              {indicator.database_type && (
                <div className="text-xs text-gray-400 mt-1">Database: {indicator.database_type}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={`h-full bg-gray-800 rounded-lg p-4 flex flex-col ${className}`}>
      {/* Header */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-white mb-2">Payload Suggestions</h3>
        <div className="text-sm text-gray-400">
          {payloadSuggestions.length > 0 
            ? `${payloadSuggestions.length} payloads available • Drag to request editor`
            : 'No payloads generated yet'
          }
        </div>
      </div>

      <div className="flex-1 overflow-y-auto space-y-4">
        {/* Analysis Summary */}
        {analysisResult && (
          <div className="bg-gray-900 rounded-lg p-3 border border-gray-600">
            <h4 className="text-sm font-semibold text-gray-300 mb-2">Analysis Summary</h4>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="text-center">
                <div className="text-blue-400 font-bold">
                  {analysisResult.injection_points?.length || 0}
                </div>
                <div className="text-gray-400">Injection Points</div>
              </div>
              <div className="text-center">
                <div className="text-yellow-400 font-bold">
                  {analysisResult.vulnerability_indicators?.length || 0}
                </div>
                <div className="text-gray-400">Indicators</div>
              </div>
              <div className="text-center">
                <div className="text-green-400 font-bold">
                  {payloadSuggestions.length}
                </div>
                <div className="text-gray-400">Payloads</div>
              </div>
            </div>
          </div>
        )}

        {/* Injection Points */}
        {renderInjectionPoints()}

        {/* Vulnerability Indicators */}
        {renderVulnerabilityIndicators()}

        {/* Payload Suggestions */}
        {payloadSuggestions.length > 0 && (
          <div>
            <h4 className="text-sm font-semibold text-gray-300 mb-3">Suggested Payloads</h4>
            <div className="space-y-3">
              {payloadSuggestions.map((payload, index) => (
                <div
                  key={index}
                  draggable
                  onDragStart={() => onPayloadDragStart(payload, index)}
                  onClick={() => onPayloadSelect(payload)}
                  className="bg-gray-900 border border-gray-600 rounded-lg p-3 cursor-pointer hover:bg-gray-800 hover:border-gray-500 transition-colors group"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded text-white ${getTypeColor(payload.type)}`}>
                        {payload.type.replace('_', ' ')}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded border ${getRiskColor(payload.risk_level)}`}>
                        {payload.risk_level}
                      </span>
                    </div>
                    <div className="text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
                      Drag to editor
                    </div>
                  </div>
                  
                  <div className="font-mono text-sm text-green-400 bg-black/30 p-2 rounded mb-2">
                    {payload.payload}
                  </div>
                  
                  <div className="text-xs text-gray-400">
                    {payload.description}
                  </div>
                  
                  {payload.applicable_points.length > 0 && (
                    <div className="text-xs text-blue-400 mt-2">
                      Applicable to: {payload.applicable_points.join(', ')}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {payloadSuggestions.length === 0 && !analysisResult && (
          <div className="text-center text-gray-400 mt-8">
            <div className="mb-4">
              <span className="text-4xl">🎯</span>
            </div>
            <p className="mb-2">No payload suggestions yet</p>
            <p className="text-sm">
              Start a chat analysis to generate SQL injection payloads
            </p>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-gray-900 rounded border border-gray-600">
        <div className="text-xs text-gray-400">
          <strong className="text-gray-300">💡 How to use:</strong>
          <br />• Click payloads to select and view details
          <br />• Drag payloads to the request editor
          <br />• Use chat to get specific recommendations
        </div>
      </div>
    </div>
  );
};

export default PayloadSuggestionPanel;