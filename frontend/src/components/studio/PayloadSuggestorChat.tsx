import React, { useState, useRef, useEffect } from 'react';
import type { 
  Message, 
  HttpRequest, 
  HttpResponse,
  PayloadSuggestion,
  PayloadAnalysisResult 
} from '../../types';
import Button from '../ui/Button';
import api from '../../lib/api';

interface PayloadSuggestorChatProps {
  httpRequest: HttpRequest;
  httpResponse: HttpResponse;
  onPayloadSuggestions: (suggestions: PayloadSuggestion[]) => void;
  onAnalysisResult: (result: PayloadAnalysisResult) => void;
  className?: string;
}

const PayloadSuggestorChat: React.FC<PayloadSuggestorChatProps> = ({
  httpRequest,
  httpResponse,
  onPayloadSuggestions,
  onAnalysisResult,
  className = ''
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (role: 'user' | 'assistant', content: string, type?: 'text' | 'code' | 'vulnerability') => {
    const newMessage: Message = {
      id: Date.now().toString(),
      role,
      content,
      timestamp: new Date(),
      type: type || 'text'
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const sendPayloadAnalysisRequest = async (userMessage?: string) => {
    if (!httpRequest.url && !httpResponse.body) {
      addMessage('assistant', 'Please provide HTTP request/response data to analyze for SQL injection opportunities.');
      return;
    }

    setIsLoading(true);

    try {
      // Create the message with embedded request/response data
      const requestDataString = JSON.stringify({
        request: httpRequest,
        response: httpResponse
      });
      
      const messageContent = userMessage 
        ? `${userMessage}\n\nAnalyze this HTTP request/response: ${requestDataString}`
        : `Please analyze this HTTP request/response for SQL injection opportunities: ${requestDataString}`;

      if (userMessage) {
        addMessage('user', userMessage);
      }

      // Send to the agent using LangServe format
      const response = await api.post('/payload-suggestor/invoke', {
        input: {
          messages: [
            {
              type: 'human',
              content: messageContent
            }
          ]
        }
      });

      const data = response.data;
      
      // Extract the last AI message
      const aiMessages = data.output?.messages?.filter((msg: any) => msg.type === 'ai') || [];
      const lastAiMessage = aiMessages[aiMessages.length - 1];
      const agentMessage = lastAiMessage?.content || 'Analysis completed.';

      addMessage('assistant', agentMessage);

      // Try to extract structured analysis from tool calls if available
      const toolMessages = data.output?.messages?.filter((msg: any) => msg.type === 'tool') || [];
      if (toolMessages.length > 0) {
        try {
          const toolResult = JSON.parse(toolMessages[0].content);
          if (toolResult.injection_points || toolResult.payload_suggestions) {
            onAnalysisResult(toolResult);
            onPayloadSuggestions(toolResult.payload_suggestions || []);
            
            // Add summary message about findings
            const summaryParts = [];
            if (toolResult.injection_points?.length > 0) {
              summaryParts.push(`Found ${toolResult.injection_points.length} potential injection points`);
            }
            if (toolResult.payload_suggestions?.length > 0) {
              summaryParts.push(`Generated ${toolResult.payload_suggestions.length} payload suggestions`);
            }
            if (toolResult.vulnerability_indicators?.length > 0) {
              summaryParts.push(`Detected ${toolResult.vulnerability_indicators.length} vulnerability indicators`);
            }

            if (summaryParts.length > 0) {
              addMessage('assistant', `📊 Analysis Summary: ${summaryParts.join(', ')}.`, 'vulnerability');
            }
          }
        } catch (e) {
          // Tool result is not JSON, continue normally
        }
      }

    } catch (error) {
      console.error('Error communicating with payload suggestor agent:', error);
      addMessage('assistant', `Error: ${error instanceof Error ? error.message : 'Failed to analyze request/response data'}`, 'text');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = () => {
    if (input.trim()) {
      sendPayloadAnalysisRequest(input);
      setInput('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleQuickAnalysis = () => {
    sendPayloadAnalysisRequest();
  };

  const renderMessage = (message: Message) => {
    const isUser = message.role === 'user';
    
    return (
      <div key={message.id} className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
        <div className={`max-w-[80%] rounded-lg p-3 ${
          isUser 
            ? 'bg-blue-600 text-white' 
            : message.type === 'vulnerability'
              ? 'bg-red-900 border border-red-700 text-red-100'
              : message.type === 'code'
                ? 'bg-gray-800 border border-gray-600 text-green-400 font-mono text-sm'
                : 'bg-gray-700 text-gray-100'
        }`}>
          <div className="whitespace-pre-wrap break-words">
            {message.content}
          </div>
          <div className="text-xs opacity-70 mt-1">
            {message.timestamp.toLocaleTimeString()}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`flex flex-col h-full bg-gray-800 rounded-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-600">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white">Payload Suggestor Agent</h3>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isLoading ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'}`}></div>
            <span className="text-sm text-gray-400">
              {isLoading ? 'Analyzing...' : 'Ready'}
            </span>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 min-h-0">
        {messages.length === 0 ? (
          <div className="text-center text-gray-400 mt-8">
            <div className="mb-4">
              <span className="text-2xl">🤖</span>
            </div>
            <p className="mb-4">Ready to analyze HTTP requests for SQL injection opportunities!</p>
            <Button 
              onClick={handleQuickAnalysis}
              disabled={!httpRequest.url && !httpResponse.body}
              className="mx-auto"
            >
              Start Analysis
            </Button>
          </div>
        ) : (
          <>
            {messages.map(renderMessage)}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-gray-600">
        <div className="flex space-x-2">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about specific injection points, payload types, or request analysis..."
            className="flex-1 bg-gray-900 border border-gray-600 rounded-lg p-3 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[60px] max-h-[120px]"
            rows={2}
            disabled={isLoading}
          />
          <div className="flex flex-col space-y-2">
            <Button
              onClick={handleSendMessage}
              disabled={!input.trim() || isLoading}
              className="h-[60px] px-4"
            >
              Send
            </Button>
          </div>
        </div>
        
        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2 mt-3">
          <button
            onClick={() => sendPayloadAnalysisRequest('Show me all high-risk injection points')}
            disabled={isLoading}
            className="text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 px-2 py-1 rounded transition-colors disabled:opacity-50"
          >
            High-Risk Points
          </button>
          <button
            onClick={() => sendPayloadAnalysisRequest('Generate boolean-based payloads')}
            disabled={isLoading}
            className="text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 px-2 py-1 rounded transition-colors disabled:opacity-50"
          >
            Boolean Payloads
          </button>
          <button
            onClick={() => sendPayloadAnalysisRequest('Check for SQL error indicators')}
            disabled={isLoading}
            className="text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 px-2 py-1 rounded transition-colors disabled:opacity-50"
          >
            Error Analysis
          </button>
          <button
            onClick={() => sendPayloadAnalysisRequest('Recommend next testing steps')}
            disabled={isLoading}
            className="text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 px-2 py-1 rounded transition-colors disabled:opacity-50"
          >
            Next Steps
          </button>
        </div>
      </div>
    </div>
  );
};

export default PayloadSuggestorChat;