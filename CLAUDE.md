# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AlgoBrain is an AI-powered pentesting assistant designed to help cybersecurity professionals identify SQL injection vulnerabilities. It's built as a LangGraph agent using Google's Gemini LLM with specialized tools for web search and vector database queries.

## Core Architecture

- **FastAPI Application**: Entry point in `src/main.py` with LangServe integration
- **LangGraph Agent**: Core logic in `src/agent.py` orchestrating conversation and tool usage
- **Tool System**: Modular tools in `src/tools/` for Google search and Qdrant vector database queries
- **Containerized Deployment**: Docker + Docker Compose setup for easy deployment

The agent follows a stateful conversation pattern with tool integration, maintaining context throughout penetration testing sessions.

## Development Commands

### Running the Application
```bash
# Using Docker Compose (recommended)
docker-compose up --build

# Direct Python execution
uvicorn src.main:app --host 0.0.0.0 --port 8001
```

### Installing Dependencies
```bash
pip install -r requirements.txt
```

### Environment Setup
Create a `.env` file with:
- `GEMINI_API_KEY`: Google Gemini API key
- `GOOGLE_CSE_API_KEY`: Google Custom Search API key  
- `GOOGLE_CSE_CX`: Google Custom Search Engine ID
- `QDRANT_URL`: Qdrant vector database URL
- `QDRANT_API_KEY`: Qdrant API key
- `COLLECTION_NAME`: Qdrant collection name for SQLi data
- `GEMINI_CHAT_MODEL`: Optional, defaults to "models/gemini-1.5-flash-preview-0514"
- `GEMINI_EMBEDDING_MODEL`: Optional, defaults to "models/embedding-001"

## Key Components

### Agent State Management
The agent uses `AgentState` TypedDict to maintain conversation history through LangGraph's state management system.

### Tool Integration
- **google_search**: Web search for vulnerability research and reconnaissance
- **Knowledge_Search**: Vector database queries for curated SQLi knowledge and payloads

**Parallel Tool Execution**: The agent can call multiple tools simultaneously in a single turn for enhanced efficiency. Examples:
- Reconnaissance: Simultaneously search Google for public information AND query vector database for relevant security techniques
- Vulnerability analysis: Parallel searches combining general web search with specialized security knowledge
- Research workflows: Multiple concurrent searches reduce response time and provide comprehensive results

### API Endpoints
- Main application serves at `http://localhost:8001`
- Interactive playground available at `http://localhost:8001/agent/playground/`
- LangServe routes mounted at `/agent` path with feedback and tracing enabled

## Technology Stack

- **Framework**: LangGraph + LangChain for agent orchestration
- **LLM**: Google Gemini via `langchain-google-genai`
- **API Server**: FastAPI with LangServe
- **Vector DB**: Qdrant for specialized security knowledge
- **Search**: Google Custom Search API
- **Containerization**: Docker with Python 3.11-slim base

## Tool Error Handling

Both search tools include comprehensive error handling:
- Missing API keys/configuration detection
- Network/API failure graceful degradation  
- Client initialization safety checks
- Formatted error responses to maintain agent flow

## Frontend Architecture

AlgoBrain includes a modern React frontend that provides an intuitive interface for penetration testing workflows.

### Frontend Stack
- **React 19.1.0**: Latest stable version with enhanced performance and concurrent features
- **TypeScript 5.8+**: Type safety and modern JavaScript features
- **Vite 7.0**: Ultra-fast build tool with HMR and optimized production builds
- **Tailwind CSS 4.0**: Utility-first CSS framework with 100x faster builds
- **TanStack Query 5.81.5**: Server state management and data fetching
- **Monaco Editor 4.7.0**: VS Code-powered code editor for payload crafting
- **Recharts 3.0.2**: Data visualization for vulnerability analysis
- **Axios 1.7+**: HTTP client for API communication

### Component Architecture
- **ChatInterface**: Main conversation UI for interacting with the AI agent
- **PayloadEditor**: Monaco-powered editor for crafting and testing SQL injection payloads
- **VulnerabilityChart**: Dashboard with charts and visualizations for vulnerability analysis
- **UI Components**: Reusable button, card, and layout components with Tailwind styling

### Development Commands

#### Frontend Development
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

#### Environment Configuration
Create `.env` file in `/frontend/` directory:
```
VITE_API_URL=http://localhost:8001
VITE_DEV_MODE=true
```

### API Integration
- Frontend communicates with FastAPI backend via HTTP requests
- API client in `src/lib/api.ts` handles communication with LangServe endpoints
- Real-time chat interface connects to `/agent/invoke` endpoint
- Support for both standard and streaming responses

### Project Structure
```
frontend/
├── src/
│   ├── components/
│   │   ├── ui/           # Reusable UI components (Button, Card)
│   │   ├── chat/         # Chat interface components
│   │   ├── editor/       # Monaco editor integration
│   │   └── dashboard/    # Vulnerability visualization
│   ├── hooks/            # Custom React hooks
│   ├── lib/              # Utilities and API client
│   ├── types/            # TypeScript type definitions
│   └── App.tsx           # Main application component
├── public/               # Static assets
├── package.json          # Dependencies and scripts
├── tsconfig.json         # TypeScript configuration
├── vite.config.ts        # Vite build configuration
├── tailwind.config.js    # Tailwind CSS configuration
└── .env.example          # Environment variables template
```

## Security Considerations

This is a defensive security tool designed for legitimate penetration testing. The agent should only be used for authorized security assessments and vulnerability research.